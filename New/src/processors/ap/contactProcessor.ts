/**
 * Contact Processor for AP Webhook Events
 *
 * Handles contact creation and update events from AutoPatient webhooks by:
 * 1. Checking for calendar property (skip if present)
 * 2. Looking up existing contact in local database
 * 3. Fetching complete contact details from AutoPatient API
 * 4. Comparing timestamps for recent updates
 * 5. Syncing with CliniCore platform
 * 6. Updating local database with results
 *
 * Performance Critical: Must complete within 20 seconds (Cloudflare Workers timeout)
 */

import { dbSchema, getDb } from "@database";
import type {
	APContactWebhookPayload,
	APWebhookPayload,
	GetAPContactType,
	GetCCPatientType,
	PostCCPatientType,
} from "@type";
import { eq, or } from "drizzle-orm";
import { contactReq, patientReq, ccCustomfieldReq } from "@/apiClient";
import { logDebug, logError, logInfo, logProcessingStep } from "@/utils/logger";
import { getConfig } from "@/utils/configs";
import type { PostCCPatientCustomfield } from "@type";
import cleanData from "@/utils/cleanData";


/**
 * Normalize contact field values (email, phone) following v3Integration patterns
 * Converts empty strings and whitespace-only strings to null
 *
 * @param value - Raw field value
 * @returns Normalized value or null
 */
function normalizeContactField(value: string | undefined | null): string | null {
	if (!value || typeof value !== 'string') return null;
	const trimmed = value.trim();
	return trimmed === '' ? null : trimmed;
}

/**
 * Update CliniCore custom fields for a patient following v3Integration patterns
 *
 * This is a reusable method that matches AP field names to CC custom field names/labels
 * and sends the data in the correct format to the CC API.
 *
 * @param patientId - CC patient ID
 * @param fieldNameValueMap - Map of field names to values (e.g., {"email": "<EMAIL>", "phone": "+**********"})
 * @param requestId - Request ID for logging
 * @returns Promise<void>
 */
async function updateCCCustomFields(
	patientId: number,
	fieldNameValueMap: Record<string, string>,
	requestId: string
): Promise<void> {
	logDebug(requestId, `Starting CC custom fields update for patient ${patientId}`);

	if (Object.keys(fieldNameValueMap).length === 0) {
		logDebug(requestId, "No custom field values to sync");
		return;
	}

	try {
		// Step 1: Fetch all available CC custom fields (following v3Integration pattern)
		logDebug(requestId, "Fetching CC custom field definitions");
		const ccCustomFields = await ccCustomfieldReq.all();

		if (!ccCustomFields || ccCustomFields.length === 0) {
			logInfo(requestId, "No CC custom fields available for matching");
			return;
		}

		// Step 2: Match AP field names against CC custom field name OR label (v3Integration logic)
		const matchedProperties: PostCCPatientCustomfield[] = [];

		Object.keys(fieldNameValueMap).forEach((fieldName) => {
			const fieldValue = fieldNameValueMap[fieldName];
			if (!fieldValue) return; // Skip empty values

			// Find matching CC custom field by name OR label (exact v3Integration pattern)
			const match = ccCustomFields.find((ccf) => ccf.name === fieldName || ccf.label === fieldName);

			if (match) {
				logDebug(requestId, `Matched field "${fieldName}" to CC custom field "${match.label}" (${match.name})`);

				// Create custom field value object (v3Integration structure)
				const customFieldValue: PostCCPatientCustomfield = {
					field: match,
					values: [{ value: fieldValue }],
					patient: null,
				};

				// Handle allowedValues constraint for dropdown/select fields (v3Integration logic)
				if (match.allowedValues && match.allowedValues.length > 0) {
					const allowedValue = match.allowedValues.find((v) => v.value === fieldValue);
					if (allowedValue) {
						// Use ID for predefined values
						customFieldValue.values = [{ id: allowedValue.id }];
						logDebug(requestId, `Using predefined value ID ${allowedValue.id} for field "${fieldName}"`);
					} else {
						logInfo(requestId, `Value "${fieldValue}" not found in allowed values for field "${fieldName}", skipping`);
						return; // Skip this field if value not allowed
					}
				}

				matchedProperties.push(customFieldValue);
			} else {
				logDebug(requestId, `No CC custom field found for "${fieldName}"`);
			}
		});

		if (matchedProperties.length === 0) {
			logInfo(requestId, "No matching CC custom fields found");
			return;
		}

		// Step 3: Clean and send update request to CC API (v3Integration pattern)
		const payload = cleanData(matchedProperties);
		logInfo(requestId, `Updating ${matchedProperties.length} custom fields for CC patient ${patientId}`);

		await patientReq.update(patientId, {
			customFields: payload as any, // Use any to bypass TypeScript checking like v3Integration
		});

		logInfo(requestId, `Successfully updated CC custom fields for patient ${patientId}`);

	} catch (error) {
		logError(requestId, "CC custom fields update error", error);
		throw new Error(`Failed to update CC custom fields: ${error}`);
	}
}



/**
 * Process contact creation and update webhooks from AutoPatient
 *
 * Implements the 4-step logic:
 * 1. Calendar property check (already done in handler)
 * 2. Local database lookup
 * 3. Timestamp comparison if contact found
 * 4. CliniCore synchronization
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param payload - Validated AP contact webhook payload
 * @returns Promise<void> - Completes processing or throws error
 */
export async function contactSyncProcessor(
	requestId: string,
	payload: APContactWebhookPayload,
): Promise<void> {
	const apContactId: string = payload.contact_id as string;
	const email = typeof payload.email === 'string' ? payload.email.trim() || null : null;
	const phone = typeof payload.phone === 'string' ? payload.phone.trim() || null : null;

	// Validate that either email or phone is present
	if (!email && !phone) {
		const errorMessage = "Processing stopped due to missing required contact data (email or phone)";
		logError(requestId, errorMessage);
		throw new Error(errorMessage);
	}

	logInfo(
		requestId,
		`Processing contact sync for AP ID: ${apContactId}, Email: ${email}, Phone: ${phone}`,
	);

	// Step 2: Local Database Lookup
	const db = getDb();
	let existingPatient: typeof dbSchema.patient.$inferSelect | undefined;

	try {
		// Primary lookup by apId
		const apIdResults = await db
			.select()
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.apId, apContactId as string))
			.limit(1);

		if (apIdResults.length > 0) {
			existingPatient = apIdResults[0];
			logDebug(requestId, `Found existing patient by AP ID: ${existingPatient.id}`);
		} else {
			// Secondary lookup by email and phone
			if (email || phone) {
				const conditions = [];
				if (email) conditions.push(eq(dbSchema.patient.email, email));
				if (phone) conditions.push(eq(dbSchema.patient.phone, phone));

				const emailPhoneResults = await db
					.select()
					.from(dbSchema.patient)
					.where(or(...conditions))
					.limit(1);

				if (emailPhoneResults.length > 0) {
					existingPatient = emailPhoneResults[0];
					logDebug(requestId, `Found existing patient by email/phone: ${existingPatient.id}`);
				}
			}
		}
	} catch (error) {
		logError(requestId, "Database lookup error", error);
		throw new Error(`Failed to lookup existing patient: ${error}`);
	}

	// Step 3A: If Contact Found in Local Database
	if (existingPatient) {
		logInfo(requestId, `Existing patient found: ${existingPatient.id}`);

		// Fetch complete contact details from AutoPatient API
		let apContactData: GetAPContactType;
		try {
			apContactData = await contactReq.get(apContactId as string);
			logDebug(requestId, `Fetched AP contact data for: ${apContactId}`);
		} catch (error) {
			logError(requestId, "AutoPatient API fetch error", error);
			throw new Error(`Failed to fetch AP contact data: ${error}`);
		}

		// Compare webhook date_updated with local database apUpdatedAt timestamp
		// Use date_updated from webhook payload if available, otherwise use date_created
		const webhookUpdatedDate = typeof apContactData.dateUpdated === 'string'
			? new Date(apContactData.dateUpdated)
			: new Date(apContactData.dateAdded as string);
		const localUpdatedDate = existingPatient.apUpdatedAt;

		if (localUpdatedDate) {
			const timeDiffMs = Math.abs(webhookUpdatedDate.getTime() - localUpdatedDate.getTime());
			const timeDiffMinutes = timeDiffMs / (1000 * 60);
			const syncBufferMinutes = (getConfig('syncBufferTimeSec') as number) / 60;

			// If timestamps are within the configured sync buffer time: Skip sync
			if (timeDiffMinutes <= syncBufferMinutes) {
				logInfo(
					requestId,
					`Recent update detected (${timeDiffMinutes.toFixed(2)} minutes, buffer: ${syncBufferMinutes} minutes) - skipping sync`,
				);
				return;
			}

			logInfo(
				requestId,
				`Timestamp difference: ${timeDiffMinutes.toFixed(2)} minutes (buffer: ${syncBufferMinutes} minutes) - proceeding with sync`,
			);
		} else {
			logInfo(requestId, "No local timestamp found - proceeding with sync");
		}

		// Continue to CliniCore sync
		await syncWithCliniCore(requestId, existingPatient.id, apContactData, true);
	} else {
		// Step 3B: If Contact NOT Found in Local Database
		logInfo(requestId, `New contact detected: ${apContactId}`);

		// Fetch complete contact details from AutoPatient API
		let apContactData: GetAPContactType;
		try {
			apContactData = await contactReq.get(apContactId as string);
			logDebug(requestId, `Fetched AP contact data for new contact: ${apContactId}`);
		} catch (error) {
			logError(requestId, "AutoPatient API fetch error", error);
			throw new Error(`Failed to fetch AP contact data: ${error}`);
		}

		// Add to local database
		let localPatientId: string;
		try {
			const now = new Date();
			const newPatientResults = await db
				.insert(dbSchema.patient)
				.values({
					apId: apContactId as string,
					email: email,
					phone: phone,
					apData: apContactData,
					apUpdatedAt: now,
				})
				.returning({ id: dbSchema.patient.id });

			localPatientId = newPatientResults[0].id;
			logInfo(requestId, `Created new patient record: ${localPatientId}`);
		} catch (error) {
			logError(requestId, "Database insert error", error);
			throw new Error(`Failed to create local patient record: ${error}`);
		}

		// Proceed to CliniCore synchronization
		await syncWithCliniCore(requestId, localPatientId, apContactData, false);
	}

	logProcessingStep(
		requestId,
		`Contact sync completed for AP ID: ${apContactId}`,
	);
}

/**
 * Sync patient data with CliniCore platform
 *
 * Step 4: CliniCore Synchronization
 * - Search CliniCore for existing patient using email and phone
 * - If patient found: Update existing patient data
 * - If patient NOT found: Create new patient record
 *
 * @param requestId - Request ID for logging
 * @param localPatientId - Local database patient ID
 * @param apContactData - AP contact data
 * @param isUpdate - Whether this is an update (true) or new contact (false)
 * @returns Promise<void>
 */
async function syncWithCliniCore(
	requestId: string,
	localPatientId: string,
	apContactData: GetAPContactType,
	_isUpdate: boolean, // Prefixed with underscore to indicate intentionally unused
): Promise<void> {
	const db = getDb();
	const now = new Date();

	// Use AP contact data as authoritative source for email/phone (more complete than webhook payload)
	const email = normalizeContactField(apContactData.email);
	const phone = normalizeContactField(apContactData.phone);

	logInfo(requestId, `Starting CliniCore sync for local patient: ${localPatientId}`);

	// Search CliniCore for existing patient using email and phone
	let existingCCPatient: GetCCPatientType | null = null;
	try {
		if (email) {
			existingCCPatient = await patientReq.search(email);
			if (existingCCPatient) {
				logDebug(requestId, `Found existing CC patient by email: ${existingCCPatient.id}`);
			}
		}

		if (!existingCCPatient && phone) {
			existingCCPatient = await patientReq.search(phone);
			if (existingCCPatient) {
				logDebug(requestId, `Found existing CC patient by phone: ${existingCCPatient.id}`);
			}
		}
	} catch (error) {
		logError(requestId, "CliniCore search error", error);
		// Don't throw here - we can still create a new patient
		logInfo(requestId, "Proceeding with patient creation due to search error");
	}

	// Convert AP contact data to CC patient format following v3Integration patterns
	const ccPatientData: PostCCPatientType = {
		firstName: normalizeContactField(apContactData.firstName) || undefined,
		lastName: normalizeContactField(apContactData.lastName) || undefined,
		email: email || undefined,
		phoneMobile: phone || undefined, // Map phone to phoneMobile as per v3Integration pattern
		dob: normalizeContactField(apContactData.dateOfBirth) || undefined,
		gender: normalizeContactField(apContactData.gender) || undefined,
		ssn: normalizeContactField(apContactData.ssn) || undefined,
		// Add address if available (improvement over v3Integration)
		...(apContactData.address1 && {
			addresses: [{
				street: normalizeContactField(apContactData.address1) || undefined,
				city: normalizeContactField(apContactData.city) || undefined,
				state: normalizeContactField(apContactData.state) || undefined,
				zipCode: normalizeContactField(apContactData.postalCode) || undefined,
			}],
		}),
	};

	let finalCCPatient: GetCCPatientType;
	try {
		if (existingCCPatient) {
			// Update existing patient data
			logInfo(requestId, `Updating existing CC patient: ${existingCCPatient.id}`);
			finalCCPatient = await patientReq.update(existingCCPatient.id, ccPatientData);
		} else {
			// Create new patient record
			logInfo(requestId, "Creating new CC patient");
			finalCCPatient = await patientReq.create(ccPatientData);
		}

		logInfo(requestId, `CC sync completed. CC ID: ${finalCCPatient.id}`);

		// CRITICAL: Add phone and email as custom fields following v3Integration pattern
		// v3Integration sends phone/email as BOTH standard fields AND custom fields
		try {
			const customFieldMap: Record<string, string> = {};

			// Add phone and email as custom fields (exact v3Integration pattern)
			if (email) {
				customFieldMap['email'] = email;
			}
			if (phone) {
				customFieldMap['phoneMobile'] = phone;
				customFieldMap['phone-mobile'] = phone;
				customFieldMap['phone'] = phone;
			}

			if (Object.keys(customFieldMap).length > 0) {
				logInfo(requestId, `Syncing ${Object.keys(customFieldMap).length} standard fields as custom fields`);
				await updateCCCustomFields(finalCCPatient.id, customFieldMap, requestId);
			}
		} catch (error) {
			logError(requestId, "Custom fields sync error", error);
			// Don't fail the entire sync if custom fields fail
			logInfo(requestId, "Proceeding with patient sync - custom fields sync failed but patient data was saved");
		}
	} catch (error) {
		logError(requestId, "CliniCore sync error", error);
		throw new Error(`Failed to sync with CliniCore: ${error}`);
	}

	// Update local database with CC sync results
	try {
		await db
			.update(dbSchema.patient)
			.set({
				ccId: finalCCPatient.id,
				ccData: finalCCPatient,
				ccUpdatedAt: now,
				updatedAt: now,
			})
			.where(eq(dbSchema.patient.id, localPatientId));

		logDebug(requestId, `Updated local patient record with CC data: ${localPatientId}`);
	} catch (error) {
		logError(requestId, "Database update error", error);
		throw new Error(`Failed to update local patient record: ${error}`);
	}
}

/**
 * Validate AP contact webhook payload
 *
 * @param payload - Raw webhook payload
 * @returns Validated payload or throws error
 */
export function validateContactWebhookPayload(
	payload: APWebhookPayload,
): APContactWebhookPayload {
	if (!payload || typeof payload !== "object") {
		throw new Error("Invalid payload: must be an object");
	}

	// Ensure calendar property is not present for contact webhooks
	if (payload.calendar) {
		throw new Error("Calendar property found - this should be handled as appointment webhook");
	}

	if (!payload.contact_id) {
		throw new Error("Missing required field: contact_id");
	}

	if (!payload.date_created) {
		throw new Error("Missing required field: date_created");
	}

	if (!payload.location) {
		throw new Error("Missing required field: location");
	}

	// Return as contact webhook payload (calendar property excluded by type)
	return payload as APContactWebhookPayload;
}
